import { computed, Injectable, signal } from '@angular/core';

/**
 * Allows storing errors for multiple FormGroups and making them available outside of the form context.
 */
@Injectable({
  providedIn: 'root',
})
export class FormErrorsService {
  #fieldErrors = signal<Map<string, string[]>>(new Map());
  #errorMessages = signal<Map<string, string>>(new Map());

  fieldErrors = this.#fieldErrors.asReadonly();
  errorMessages = this.#errorMessages.asReadonly();

  erroneousScopes = computed<Set<string>>(() => {
    const erroneousFieldIds = Array.from(this.#fieldErrors().entries())
      .filter(([_, erroneousRules]) => erroneousRules.length > 0)
      .map(([fieldId]) => fieldId);

    const scopes = new Set<string>();
    for (const fieldId of erroneousFieldIds) {
      const fieldIdParts = fieldId.split('.');
      let prefix = '';

      for (const fieldIdPart of fieldIdParts) {
        for (const part of fieldIdPart.split('-')) {
          prefix = prefix.endsWith('.')
            ? `${prefix}${part}`
            : prefix
            ? `${prefix}-${part}`
            : part;
          scopes.add(prefix);
        }
        prefix += '.';
      }
    }

    return scopes;
  });

  /**
   * Update errors for a specific field
   */
  updateFieldErrors(fieldId: string, erroneousRules: string[] | null) {
    const currentErrors = new Map(this.#fieldErrors());

    if (!erroneousRules || erroneousRules.length === 0) {
      currentErrors.set(fieldId, []);
    } else {
      currentErrors.set(fieldId, erroneousRules);
    }

    this.#fieldErrors.set(currentErrors);
  }

  setErrorMessages(errorMessages: Map<string, string>) {
    this.#errorMessages.set(errorMessages);
  }
}
