{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "moduleResolution": "bundler",
    "declaration": true,
    "declarationMap": true,
    "inlineSources": true,
    "resolveJsonModule": true // Required to import the generated rules JSON into a typescript file.
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
