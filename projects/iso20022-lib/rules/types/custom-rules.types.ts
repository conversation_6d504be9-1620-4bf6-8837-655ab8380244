import { ConditionalRule, Rule } from './validation-rules.types';

// ---- Rules ----
export type CustomRuleWithBaseKeysAndUsageGuidelineRules<
  I = string | undefined,
  D = string | undefined
> = Rule<I, D> & {
  baseKeys?: string[]; // This allows for defining a rule that applies to multiple keys or to just extract the lengthy key into a single place.
  usageGuidelineRules?: string[]; // This allows for defining prefixes, e.g. to denote the ID from the usage guidelines (e.g. "R17", "R18")
};
export type CustomRuleWithUsageGuidelineRules<
  I = string | undefined,
  D = string | undefined
> = Rule<I, D> & {
  usageGuidelineRules?: string[];
};
export type CustomConditionalRuleWithUsageGuidelineRules<
  I = string | undefined,
  D = string | undefined
> = ConditionalRule<I, D> & {
  usageGuidelineRules?: string[];
};

// ---- Required Definitions ----

export type AndDefinition = string[]; // E.g. ["field1", "field2"] means that both field1 and field2 must be present.

export type OrDefinition = (string | AndDefinition)[]; // E.g. ["field1", ["field2", "field3"]] means that either field1 must be present or both field2 and field3 must be present.

export type RequiredDefinition =
  | {
      or?: OrDefinition;
    }
  | { and?: AndDefinition };
