import { Directive, effect, input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { Rule } from '@helaba/iso20022-lib/rules';
import {
  getChangedFieldIds,
  applyRelevantRules,
  flattenFieldId,
  getFieldIdsFromFormGroup,
} from '@helaba/iso20022-lib/util';

@Directive({
  selector: '[formRules]',
})
export class FormRulesDirective {
  targetFormGroup = input.required<FormGroup>({ alias: 'formGroup' });
  validationRules = input.required<Rule<string, undefined>[]>({
    alias: 'formRules',
  });
  affectedFields = input.required<Record<string, string[]>>(); // Contains flattened field IDs (as denoted in the rules).

  #activeSubscriptions: Subscription[] = [];
  #previousFormValues: any = {}; // Store previous form values to detect changes

  constructor() {
    effect((onCleanup) => {
      // Clean up any existing subscriptions when the formGroup changes or component is destroyed.
      this.cleanup();

      /**
       * Apply custom ValidatorFn to all form inputs
       */
      for (const [fieldName, control] of Object.entries(
        this.targetFormGroup().controls
      )) {
        applyRelevantRules(fieldName, control, this.validationRules());
      }

      // Initiate the 'previousFormValues' with the current values of the form inputs
      this.#previousFormValues = this.targetFormGroup().value;

      /**
       * We have to subscribe to all inputs, since on-change is only triggered if
       * the input happened on that specific field, but we need to check the target form-inputs
       */
      this.#activeSubscriptions.push(
        this.targetFormGroup().valueChanges.subscribe((currentValues) => {
          const changedFieldIds: string[] = getChangedFieldIds(
            this.#previousFormValues,
            currentValues
          );

          this.#previousFormValues = currentValues; // Update previous values for next change detection

          // Figure out which other fields are connected to the changed fields via conditional rules
          const affectedFlattenedFieldIdsForValueChanges: Set<string> = new Set(
            changedFieldIds.flatMap(
              (changedFieldId) =>
                this.affectedFields()[flattenFieldId(changedFieldId)] || []
            )
          );

          const allFieldIds = getFieldIdsFromFormGroup(this.targetFormGroup());
          for (const fieldId of allFieldIds) {
            if (
              affectedFlattenedFieldIdsForValueChanges.has(
                flattenFieldId(fieldId)
              )
            ) {
              const control = this.targetFormGroup().get(fieldId);
              if (!control) {
                throw new Error(
                  `FormRulesDirective: Control for field "${fieldId}" not found in the form group. Ensure that the field exists in the form group.`
                );
              }
              control.updateValueAndValidity({ onlySelf: true }); // Do not bubble up the change event to the parent as this would again call 'onFormValueChanges' and cause an infinite loop.
            }
          }
        })
      );

      onCleanup(() => {
        this.cleanup();
      });
    });
  }

  private cleanup() {
    this.#activeSubscriptions.forEach((sub) => sub.unsubscribe());
    this.#activeSubscriptions = [];
    this.#previousFormValues = {};
  }
}
