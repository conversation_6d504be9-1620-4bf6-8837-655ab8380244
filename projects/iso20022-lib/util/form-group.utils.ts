import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
} from '@angular/forms';
import {
  isFormArray,
  isFormGroup,
  isLeafControl,
  isObject,
  isStringArray,
} from './type.utils';

/**
 * Compare previous and current form group values to find changed fields.
 * @param prev the previous form group value.
 * @param curr the current form group value.
 * @param path the path to the current field in the form group.
 * @returns An array of field IDs that have changed.
 */
export function getChangedFieldIds(
  prev: unknown,
  curr: unknown,
  path: string[] = []
): string[] {
  const changes: string[] = [];

  if (prev === curr) {
    return changes; // No changes
  }

  if (Array.isArray(prev) || Array.isArray(curr)) {
    if (
      !(
        (Array.isArray(prev) && Array.isArray(curr)) ||
        typeof prev === 'undefined' ||
        typeof curr === 'undefined'
      )
    ) {
      throw new Error(
        `Cannot compare array with non-array: ${JSON.stringify(
          prev
        )} vs ${JSON.stringify(curr)}`
      );
    }

    if (typeof prev === 'undefined' || typeof curr === 'undefined') {
      const definedArray = Array.isArray(prev) ? prev : curr;
      changes.push(...getFieldIds(definedArray, path));
    } else if (Array.isArray(prev) && Array.isArray(curr)) {
      if (isStringArray(prev) && isStringArray(curr)) {
        if (
          prev.length !== curr.length ||
          !prev.every((val, i) => val === curr[i])
        ) {
          changes.push(path.join('.'));
        }
      } else if (prev.every(isObject) && curr.every(isObject)) {
        const maxLength = Math.max(prev.length, curr.length);
        for (let i = 0; i < maxLength; i++) {
          changes.push(
            ...getChangedFieldIds(prev[i], curr[i], [...path, `${i}`])
          );
        }
      } else {
        throw new Error(
          `Cannot compare arrays with mixed types: ${JSON.stringify(
            prev
          )} vs ${JSON.stringify(curr)}`
        );
      }
    }
  } else if (isObject(prev) || isObject(curr)) {
    if (
      !(
        (isObject(prev) && isObject(curr)) ||
        typeof prev === 'undefined' ||
        typeof curr === 'undefined'
      )
    ) {
      throw new Error(
        `Cannot compare object with non-object: ${JSON.stringify(
          prev
        )} vs ${JSON.stringify(curr)}`
      );
    }

    if (typeof prev === 'undefined' || typeof curr === 'undefined') {
      const definedObject = isObject(prev) ? prev : curr;
      changes.push(...getFieldIds(definedObject, path));
    } else if (isObject(prev) && isObject(curr)) {
      const keys = new Set([...Object.keys(prev), ...Object.keys(curr)]);
      for (const key of keys) {
        const subPath = [...path, key];
        if (!(key in prev) || !(key in curr)) {
          changes.push(subPath.join('.'));
        } else {
          changes.push(...getChangedFieldIds(prev[key], curr[key], subPath));
        }
      }
    }
  } else if (prev !== curr) {
    changes.push(path.join('.'));
  }

  return changes;
}

function getFieldIds(val: unknown, path: string[] = []): string[] {
  const id = path.join('.');
  const results: string[] = [];

  if (Array.isArray(val)) {
    // Include the array itself
    if (id) results.push(id);

    for (const [index, item] of val.entries()) {
      results.push(...getFieldIds(item, [...path, index.toString()]));
    }
  } else if (isObject(val)) {
    // Do NOT include plain objects
    for (const [key, value] of Object.entries(val)) {
      results.push(...getFieldIds(value, [...path, key]));
    }
  } else {
    if (id) results.push(id);
  }

  return results;
}

export function getFieldIdsFromFormGroup(formGroup: FormGroup): string[] {
  if (formGroup === null || formGroup === undefined) {
    return [];
  }
  const fieldIds: string[] = [];
  for (const [fieldName, control] of Object.entries(formGroup.controls)) {
    if (isLeafControl(control)) {
      fieldIds.push(fieldName);
    } else if (isFormGroup(control)) {
      const nestedFieldIds = getFieldIdsFromFormGroup(control);
      fieldIds.push(
        ...nestedFieldIds.map((fieldId) => `${fieldName}.${fieldId}`)
      );
    } else if (isFormArray(control)) {
      fieldIds.push(fieldName);
      control.controls.forEach((item, index) => {
        if (isFormGroup(item)) {
          const nestedFieldIds = getFieldIdsFromFormGroup(item);
          fieldIds.push(
            ...nestedFieldIds.map(
              (fieldId) => `${fieldName}.${index}.${fieldId}`
            )
          );
        }
      });
    }
  }
  return fieldIds;
}

export function getFieldIdsFromFormValues(formValues: unknown): string[] {
  if (formValues === null || formValues === undefined) {
    return [];
  }
  const fieldIds: string[] = [];
  if (!isObject(formValues)) {
    throw new Error(
      `Expected formValues to be an object, but got: ${formValues}`
    );
  }
  for (const [fieldName, value] of Object.entries(formValues)) {
    if (typeof value === 'string' || value === null || value === undefined) {
      fieldIds.push(fieldName);
    } else if (isObject(value)) {
      const nestedFieldIds = getFieldIdsFromFormValues(value);
      fieldIds.push(
        ...nestedFieldIds.map((fieldId) => `${fieldName}.${fieldId}`)
      );
    } else if (Array.isArray(value)) {
      fieldIds.push(fieldName);
      value.forEach((item, index) => {
        if (isObject(item)) {
          const nestedFieldIds = getFieldIdsFromFormValues(item);
          fieldIds.push(
            ...nestedFieldIds.map(
              (fieldId) => `${fieldName}.${index}.${fieldId}`
            )
          );
        }
      });
    }
  }

  return fieldIds;
}

/**
 * Gets the full path of a form control within a form structure
 */
export function getFieldIdForControl(control: AbstractControl): string | null {
  if (!control.parent) {
    return null; // This is the root control
  }

  const path: string[] = [];
  let currentControl = control;

  // Traverse up the parent chain
  while (currentControl.parent) {
    const parent = currentControl.parent;

    if (isFormGroup(parent)) {
      // Find the key in FormGroup
      const key = Object.keys(parent.controls).find(
        (k) => parent.controls[k] === currentControl
      );
      if (key !== undefined) {
        path.unshift(key);
      }
    } else if (isFormArray(parent)) {
      // Find the index in FormArray
      const index = parent.controls.findIndex((c) => c === currentControl);
      if (index !== -1) {
        path.unshift(index.toString());
      }
    }

    currentControl = parent;
  }

  return path.length > 0 ? path.join('.') : null;
}

/**
 *
 * @param flattenedFieldId the flattened field ID to find, e.g. "field1-nestedField1".
 * @param formGroup the FormGroup to search in.
 * @returns the AbstractControls matching the flattened field ID.
 */
export function findControlsByFlattenedFieldId(
  flattenedFieldId: string,
  formGroup: FormGroup
): (
  | FormControl<string | null | undefined>
  | FormArray<FormControl<string | null | undefined> | FormGroup>
)[] {
  const rootLevelFieldNames = Object.keys(formGroup.controls);

  if (rootLevelFieldNames.includes(flattenedFieldId)) {
    const control = formGroup.get(flattenedFieldId);
    if (!control) {
      throw new Error(
        `Cannot find control for field name "${flattenedFieldId}" in form group.`
      );
    }
    if (!isLeafControl(control) && !isFormArray(control)) {
      throw new Error(
        `Expected control for field name "${flattenedFieldId}" to be a FormControl or FormArray, but it is not.`
      );
    }
    return [control];
  }

  const parts = flattenedFieldId.split('-');
  if (parts.length === 0) {
    throw new Error(`'flattenedFieldName' is empty: "${flattenedFieldId}".`);
  }
  const rootLevelFieldName = parts.reduce<string>((acc, part) => {
    if (rootLevelFieldNames.includes(acc)) {
      return acc;
    }
    return acc ? `${acc}-${part}` : part;
  }, '');

  const control = formGroup.get(rootLevelFieldName);
  if (!control) {
    throw new Error(
      `Cannot find control for field name "${rootLevelFieldName}" in form group.`
    );
  }

  // We go one level deeper and only need the remaining field names to find the control.
  const remainingFlattenedFieldId = flattenedFieldId.slice(
    rootLevelFieldName.length + 1
  ); // Remove the root level field name + 1 character for the hyphen

  if (isFormGroup(control)) {
    return findControlsByFlattenedFieldId(remainingFlattenedFieldId, control);
  }

  if (isFormArray(control)) {
    const controls = [];
    for (const [index, nestedFormGroup] of control.controls.entries()) {
      if (!isFormGroup(nestedFormGroup)) {
        throw new Error(
          `Expected control at index ${index} in form array "${rootLevelFieldName}" to be a FormGroup, but it is not.`
        );
      }
      controls.push(
        ...findControlsByFlattenedFieldId(
          remainingFlattenedFieldId,
          nestedFormGroup
        )
      );
    }
    return controls;
  }

  throw new Error(
    `Expected control for field name "${flattenedFieldId}" to be a FormControl, FormGroup, or FormArray, but it is none of those.`
  );
}
