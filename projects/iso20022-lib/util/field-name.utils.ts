import { isNumberString } from './type.utils';

/**
 * Splits a PascalCase string into an array of words.
 * @param str the string to split.
 * @returns the individual parts.
 * @example
 * splitPascalCase("GroupHeader") // ["Group", "Header"]
 * splitPascalCase("FIToFICustomerCreditTransferV08") // ["FI", "To", "FI", "Customer", "Credit", "Transfer", "V08"]
 * splitPascalCase("CreditTransferTransactionInformation") // ["Credit", "Transfer", "Transaction", "Information"]
 * splitPascalCase("PreviousInstructingAgent1Account") // ["Previous", "Instructing", "Agent1", "Account"]
 * splitPascalCase("IBAN") // ["IBAN"]
 */
export function splitPascalCase(str: string): string[] {
  const result: string[] = [];
  let word = '';

  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    const prev = str[i - 1];
    const next = str[i + 1];

    const isUpper = /[A-Z]/.test(char);
    const isPrevLowerAlphaNum =
      typeof prev !== 'undefined' && /[a-z0-9]/.test(prev);
    const isNextLowerAlphaNum =
      typeof next !== 'undefined' && /[a-z0-9]/.test(next);

    if (i > 0 && isUpper && (isPrevLowerAlphaNum || isNextLowerAlphaNum)) {
      result.push(word);
      word = char;
    } else {
      word += char;
    }
  }

  if (word) result.push(word);

  return result.filter((word) => !!word);
}

/**
 * Flattens an array field name by removing indices and joining the parts with a hyphen.
 * @param fieldId the field id to flatten, e.g. "field1.0.nestedField1".
 * @returns the flattened field name, e.g. "field1-nestedField1".
 */
export function flattenFieldId(fieldId: string): string {
  if (!fieldId.includes('.')) {
    return fieldId;
  }
  const parts = fieldId.split('.');
  return parts.reduce<string>((acc, part) => {
    if (isNumberString(part)) {
      // We skip indices
      return acc;
    }

    return acc ? `${acc}-${part}` : part;
  }, '');
}

export function getCurrentFormLevelFieldName(fieldId: string): string {
  const parts = fieldId.split('.');
  return parts[0];
}
