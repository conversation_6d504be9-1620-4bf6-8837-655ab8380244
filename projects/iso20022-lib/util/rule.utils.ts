import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  Condition,
  ConditionalRule,
  isCondition,
  Rule,
} from '@helaba/iso20022-lib/rules';
import {
  isFormArray,
  isFormGroup,
  isLeafControl,
  isNonEmptyString,
  isPresent,
  isStringArray,
} from './type.utils';
import { flattenFieldId } from './field-name.utils';
import {
  findControlsByFlattenedFieldId,
  getFieldIdForControl,
} from './form-group.utils';

export function applyRelevantRules(
  fieldId: string,
  control: AbstractControl,
  rules: Rule<string, undefined>[]
) {
  const flattenedFieldId = flattenFieldId(fieldId);
  const rulesForField = rules.filter((r) =>
    r.type === 'condition'
      ? r.rules.some((subRule) => subRule.target === flattenedFieldId)
      : r.target === flattenedFieldId
  );
  if (rulesForField.length > 0) {
    control.addValidators(getCustomValidatorFn(rulesForField, fieldId));
  }
}

/**
 * Custom ValidatorFn to combine Validators.Foo with cross-reference rules
 * @param rules Relevant rules for the control
 * @param fieldId The field ID to which the rules apply
 * @returns Custom ValidatorFn that returns cumulative ValidationErrors in a custom format { [ruleId]: unknown } or null if everything is fine
 */
function getCustomValidatorFn(
  rules: Array<Rule<string, undefined>>,
  fieldId: string
): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.parent) {
      return null;
    }

    const errors: ValidationErrors = {};

    for (const rule of rules) {
      if (rule.type !== 'condition') {
        const basicRuleErrors = getBasicRuleErrors(control, rule);
        if (!basicRuleErrors) {
          continue;
        }
        // We are actually only interested in the rule id, as that is simply turned into an error message which contains the reason. No need to know whether it is a 'required' or 'pattern' error etc.
        // We still save the error object, in case we need it later on.
        errors[rule.id] = basicRuleErrors;
      } else {
        const conditionalRuleErrors = conditionValidator(
          fieldId,
          control,
          rule
        );
        if (!conditionalRuleErrors) {
          continue;
        }
        for (const [nestedRuleId, errorValue] of Object.entries(
          conditionalRuleErrors
        )) {
          // We are actually only interested in the rule id, as that is simply turned into an error message which contains the reason. No need to know whether it is a 'required' or 'pattern' error etc.
          // We still save the error object, in case we need it later on.
          errors[nestedRuleId] = errorValue;
        }
      }
    }

    return Object.keys(errors).length > 0 ? errors : null;
  };
}

function getBasicRuleErrors(
  control: AbstractControl,
  ruleToApply: Rule<string, undefined>
): ValidationErrors | null {
  switch (ruleToApply.type) {
    case 'required':
      if (ruleToApply.value === false) {
        return null;
      }
      return requiredValidator(control);
    case 'prohibited':
      if (ruleToApply.value === false) {
        return null;
      }
      return prohibitedValidator(control);
    case 'maxLength':
      return maxStringLengthValidator(control, ruleToApply.value);
    case 'maxItems':
      return Validators.maxLength(ruleToApply.value)(control);
    case 'pattern':
      return patternValidator(control, ruleToApply.value);
    case 'value':
      if (ruleToApply.isEqual === true) {
        return patternValidator(control, `^${ruleToApply.value}$`);
      }
      return patternValidator(control, `^(?!${ruleToApply.value}$).*`);
    case 'contains':
      return containsValidator(
        control,
        ruleToApply.value,
        ruleToApply.contains
      );
  }
  return null;
}

function getRootFormGroup(control: AbstractControl): FormGroup {
  let currentControl = control;
  while (currentControl.parent) {
    currentControl = currentControl.parent;
  }
  return currentControl as FormGroup;
}

function conditionValidator(
  fieldId: string,
  control: AbstractControl,
  conditionalRule: ConditionalRule<string, undefined>
): Record<string, ValidationErrors[]> | null {
  const rootFormGroup = getRootFormGroup(control);
  if (!rootFormGroup) {
    console.warn(
      'conditionValidator: No root form group found for the control.',
      control
    );
    return null;
  }
  const conditionsConnector = conditionalRule.conditionsConnector || 'and';
  const conditionsMet: boolean[] = [];
  const allFieldIdsForWhichConditionsAreMet = new Set<string>();

  for (const condition of conditionalRule.conditions) {
    if (isCondition(condition)) {
      const fieldIdsForWhichConditionIsMet = findFieldIdsForWhichConditionIsMet(
        condition,
        rootFormGroup
      );
      if (
        conditionsConnector === 'and' &&
        fieldIdsForWhichConditionIsMet.size === 0
      ) {
        // we don't have to check every condition, since all must be met
        return null;
      }
      fieldIdsForWhichConditionIsMet.forEach((fieldId) =>
        allFieldIdsForWhichConditionsAreMet.add(fieldId)
      );
      if (fieldIdsForWhichConditionIsMet.size > 0) {
        conditionsMet.push(true);
      }
    } else {
      const nestedConditionsMet: boolean[] = [];
      const allFieldIdsForWhichNestedConditionsAreMet = new Set<string>();

      for (const nestedCondition of condition.conditions) {
        const fieldIdsForWhichNestedConditionIsMet =
          findFieldIdsForWhichConditionIsMet(nestedCondition, rootFormGroup);
        fieldIdsForWhichNestedConditionIsMet.forEach((fieldId) =>
          allFieldIdsForWhichNestedConditionsAreMet.add(fieldId)
        );
        if (fieldIdsForWhichNestedConditionIsMet.size > 0) {
          nestedConditionsMet.push(true);
        }
      }

      allFieldIdsForWhichNestedConditionsAreMet.forEach((fieldId) =>
        allFieldIdsForWhichConditionsAreMet.add(fieldId)
      );

      const nestedConditionsConnector = condition.conditionsConnector || 'and';

      conditionsMet.push(
        nestedConditionsConnector === 'and'
          ? nestedConditionsMet.every(Boolean)
          : nestedConditionsMet.some(Boolean)
      );
    }
  }

  const sufficientConditionsMet =
    conditionsConnector === 'and'
      ? conditionsMet.every(Boolean)
      : conditionsMet.some(Boolean);

  if (!sufficientConditionsMet) {
    return null;
  }

  // Sufficient conditions are met, now we need to check all conditional rules (conditionalRule.rules) to figure out which rule IDs to return.
  // It is not enough to just check the rules for the current control's field:
  // - If the 'rulesConnector' is 'or' and a conditional rule with a target different from the current field is fulfilled, there is no error -> return null.
  // - If a conditional rule with a target different from the current field is broken and the current field shows up in the conditions met, we return the rule ID of that other rule as we want to show the error in all connected places.
  const errorsForRuleIdAndFieldId: Record<
    string,
    Record<string, ValidationErrors | null>
  > = {};
  for (const rule of conditionalRule.rules) {
    const target = rule.target;
    const relevantControls = findControlsByFlattenedFieldId(
      target,
      rootFormGroup
    );
    errorsForRuleIdAndFieldId[rule.id] = {};
    for (const relevantControl of relevantControls) {
      const relevantControlFieldId = getFieldIdForControl(relevantControl);
      if (!relevantControlFieldId) {
        throw new Error(
          `conditionValidator: Could not find fieldId for control ${relevantControl}`
        );
      }
      const ruleErrors = getBasicRuleErrors(relevantControl, rule);
      errorsForRuleIdAndFieldId[rule.id] = {
        ...errorsForRuleIdAndFieldId[rule.id],
        [relevantControlFieldId]: ruleErrors,
      };
    }
  }

  const allErrors: (ValidationErrors | null)[] = Object.values(
    errorsForRuleIdAndFieldId
  )
    .map((errorsForFieldId) => Object.values(errorsForFieldId))
    .flat();

  const rulesConnector = conditionalRule.rulesConnector || 'and';
  if (rulesConnector === 'or') {
    if (allErrors.some((error) => error === null)) {
      // If it is sufficient that at least one rule is met, we don't want to show errors for the remaining rules, thus we just return null.
      return null;
    }
  } else {
    if (allErrors.every((error) => error === null)) {
      return null;
    }
  }

  const nonEmptyErrorsForRuleIdAndFieldId: Record<
    string,
    Record<string, ValidationErrors>
  > = Object.fromEntries(
    Object.entries(errorsForRuleIdAndFieldId)
      .filter(([, errorsForFieldId]) =>
        Object.values(errorsForFieldId).some((error) => error !== null)
      )
      .map(([ruleId, errorsForFieldId]) => [
        ruleId,
        Object.fromEntries(
          Object.entries(errorsForFieldId).filter(([, error]) => error !== null)
        ) as Record<string, ValidationErrors>,
      ])
  );

  // If the current control is in 'allFieldIdsForWhichConditionsAreMet', we want to display all erroneous rule IDs for the control.
  if (allFieldIdsForWhichConditionsAreMet.has(fieldId)) {
    return Object.fromEntries(
      Object.entries(nonEmptyErrorsForRuleIdAndFieldId).map(
        ([ruleId, errorsForFieldId]) => [
          ruleId,
          Object.values(errorsForFieldId),
        ]
      )
    );
  }

  // Only return the erroneous rule IDs for the current control.
  return Object.fromEntries(
    Object.entries(nonEmptyErrorsForRuleIdAndFieldId)
      .filter(([, errorsForFieldId]) => fieldId in errorsForFieldId)
      .map(([ruleId, errorsForFieldId]) => [
        ruleId,
        [errorsForFieldId[fieldId]],
      ])
  );
}

function findFieldIdsForWhichConditionIsMet(
  condition: Condition,
  rootFormGroup: FormGroup
): Set<string> {
  const relatedControls = findControlsByFlattenedFieldId(
    condition.field,
    rootFormGroup
  );

  const fieldIds = new Set<string>();

  for (const relatedControl of relatedControls) {
    const fieldId = getFieldIdForControl(relatedControl);
    if (!fieldId) {
      throw new Error(
        `ConditionValidator: Could not find field ID for control ${relatedControl}.`
      );
    }
    const relatedValue = relatedControl.value;

    let conditionMet = false;
    switch (condition.type) {
      case 'value':
        conditionMet = isStringArray(relatedValue)
          ? relatedValue.some((entry) => entry === condition.value)
          : relatedValue === condition.value;
        break;
      case 'present':
        conditionMet = isPresent(relatedValue);
        break;
      case 'notEqual':
        const otherFieldControls = findControlsByFlattenedFieldId(
          condition.otherField,
          rootFormGroup
        );
        const otherFieldValues = otherFieldControls.map(
          (control) => control.value
        );
        for (const otherFieldValue of otherFieldValues) {
          if (!isNonEmptyString(otherFieldValue)) {
            continue;
          }
          if (Array.isArray(otherFieldValue)) {
            for (const otherFieldValueEntry of otherFieldValue) {
              if (fieldValueExists(otherFieldValueEntry, relatedValue)) {
                conditionMet = false;
                break;
              }
            }
          } else {
            if (fieldValueExists(otherFieldValue, relatedValue)) {
              conditionMet = false;
              break;
            }
          }
        }
        conditionMet = true;
    }

    if (conditionMet) {
      fieldIds.add(fieldId);
    }
  }

  return fieldIds;
}

function fieldValueExists(
  value: string,
  otherValue: string | (string | null | undefined)[] | null | undefined
): boolean {
  if (!isPresent(otherValue)) {
    return false;
  }
  if (isStringArray(otherValue)) {
    if (otherValue.some((otherValueEntry) => otherValueEntry === value)) {
      return true;
    }
  } else {
    if (otherValue === value) {
      return true;
    }
  }
  return false;
}

/**
 * Validates that the control's value is present (not null, undefined, or empty).
 * If the value is an array, it checks that at least one entry is present.
 * @param control the control to validate
 * @returns null if the value is present, or an object '{required: true}' if it is not present
 */
function requiredValidator(control: AbstractControl): ValidationErrors | null {
  return isPresent(control.value) ? null : { required: true };
}

/**
 * Validates that the control's value is not present.
 * If the value is an array, it checks that the array is empty or only has empty values.
 * @param control the control to validate
 * @returns null if the value is not present, or an object '{prohibitedValue: value}' if it is present
 */
function prohibitedValidator(
  control: AbstractControl
): ValidationErrors | null {
  return isPresent(control.value) ? { prohibitedValue: control.value } : null;
}

/**
 * Validates that the value of the control does not exceed the specified maximum length.
 * If the value is an array, it checks the length of each entry and returns the longest entry's length.
 * @param control the control to validate
 * @param maxLength the maximum allowed length for the string or array entries
 * @returns null if the value is within the maximum length, or an object '{maxlength: {requiredLength: 10, actualLength: 12}}' if it exceeds the maximum length
 */
function maxStringLengthValidator(
  control: AbstractControl,
  maxLength: number
): ValidationErrors | null {
  const value = control.value;
  if (!isPresent(value)) {
    return null;
  }

  if (Array.isArray(value)) {
    const longestEntryLength = value.reduce(
      (max, entry) => Math.max(max, entry?.length || 0),
      0
    );
    return longestEntryLength <= maxLength
      ? null
      : {
          maxlength: {
            requiredLength: maxLength,
            actualLength: longestEntryLength,
          },
        };
  }

  return value.length <= maxLength
    ? null
    : { maxlength: { requiredLength: maxLength, actualLength: value.length } };
}

/**
 * Checks if the value of the control matches the given pattern.
 * If the value is an array, it checks each entry against the pattern.
 * @param control the control to validate
 * @param pattern the regex pattern to match against
 * @returns null if the value matches the pattern, or an object '{pattern: {requiredPattern: '^[a-zA-Z ]*$', actualValue: '1'}}' if it does not match
 */
function patternValidator(
  control: AbstractControl,
  pattern: string
): ValidationErrors | null {
  const value = control.value;
  if (!isPresent(value)) {
    return null;
  }

  if (Array.isArray(value)) {
    const invalidEntries = value.filter(
      (entry) => !new RegExp(pattern).test(entry)
    );
    return invalidEntries.length > 0
      ? {
          pattern: {
            requiredPattern: pattern,
            actualValue: invalidEntries.join(', '),
          },
        }
      : null;
  }

  return value.match(new RegExp(pattern))
    ? null
    : {
        pattern: {
          requiredPattern: pattern,
          actualValue: value,
        },
      };
}

/**
 * Validates that the control's value does or does not contain the values of the 'otherFields'.
 * This is mostly meant for "the target field must not contain the value of another field" validation.
 * Nonetheless, we provide logic for the 'contains: true' case as well: At least one of the 'otherFields' values must be present in at least one of the target field's values.
 * @param control the control to validate
 * @param otherFields an array of field names whose values should be checked against the control's value
 * @param contains if true, the control's value must contain at least one of the 'otherFields' values; if false, the control's value must not contain any of the 'otherFields' values
 * @returns null if the validation passes, or an object '{contains: value}' if the control's value wrongly contains any of the 'otherFields' values, or '{notContains: otherValues}' if it wrongly does not contain any of the 'otherFields' values
 */
function containsValidator(
  control: AbstractControl,
  otherFields: string[],
  contains: boolean
): ValidationErrors | null {
  const controlValues = control.value;
  const values = !isPresent(controlValues)
    ? []
    : Array.isArray(controlValues)
    ? controlValues.filter(isPresent)
    : [controlValues];

  // 'flatMap' because the value of another field can be an array of strings
  const otherValues = otherFields.flatMap(
    (field) => control.root.get(field)?.value
  );

  if (!contains) {
    if (!isPresent(values)) {
      return null;
    }

    for (const value of values) {
      // 'value' might e.g. be an address line like "Street 13, 12345 City". We need to check against all parts of the value getting rid of spaces and commas.
      const valueParts = value.split(/[\s,]+/).filter(Boolean);
      if (valueParts.some((part: string) => otherValues.includes(part))) {
        return { contains: value };
      }
    }

    return null;
  } else {
    if (!isPresent(otherValues)) {
      return null;
    }

    for (const value of values) {
      // 'value' might e.g. be an address line like "Street 13, 12345 City". We need to check against all parts of the value getting rid of spaces and commas.
      const valueParts = value.split(/[\s,]+/).filter(Boolean);
      if (valueParts.some((part: string) => otherValues.includes(part))) {
        return null;
      }
    }

    return { notContains: otherValues.join(', ') };
  }
}
