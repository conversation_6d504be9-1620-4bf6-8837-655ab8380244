import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
} from '@angular/forms';

export type SelectOption<T> = {
  key: string;
  label: string;
  value: T;
};

export function isPresent(value: unknown): boolean {
  if (value === null || value === undefined) {
    return false;
  }
  if (typeof value === 'string') {
    return isNonEmptyString(value);
  }
  if (isObject(value)) {
    return Object.values(value).some((val) => isPresent(val));
  }
  if (Array.isArray(value)) {
    return value.length > 0 && value.some(isPresent);
  }

  throw new Error(
    `isPresent called with unsupported type: ${typeof value}. Only strings, objects, and arrays are supported.`
  );
}

export function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.trim() !== '';
}

export function isObject(obj: unknown): obj is Record<string, unknown> {
  return typeof obj === 'object' && obj !== null && !Array.isArray(obj);
}

export function isStringArray(arr: unknown): arr is string[] {
  return (
    typeof arr !== 'undefined' &&
    arr !== null &&
    Array.isArray(arr) &&
    arr.every((element) => typeof element === 'string')
  );
}

export function isFormGroup(control: AbstractControl): control is FormGroup {
  return (
    control instanceof FormGroup &&
    control.value !== null &&
    control.value !== undefined &&
    typeof control.value === 'object' &&
    !Array.isArray(control.value) &&
    'controls' in control &&
    control.controls !== null &&
    control.controls !== undefined &&
    typeof control.controls === 'object' &&
    !Array.isArray(control.controls)
  );
}

export function isFormArray(control: AbstractControl): control is FormArray {
  return (
    control instanceof FormArray &&
    control.value !== null &&
    control.value !== undefined &&
    Array.isArray(control.value) &&
    'controls' in control &&
    control.controls !== null &&
    control.controls !== undefined &&
    Array.isArray(control.controls)
  );
}

export function isLeafControl(
  control: AbstractControl
): control is FormControl<string | null | undefined> {
  return (
    control instanceof FormControl &&
    (control.value === null ||
      control.value === undefined ||
      typeof control.value === 'string') &&
    (!('controls' in control) ||
      control.controls === undefined ||
      control.controls === null)
  );
}

export function isNumberString(value: string): boolean {
  return /^\d+$/.test(value);
}
