import {
  ChangeDetectionStrategy,
  Component,
  Signal,
  computed,
  inject,
  input,
  TemplateRef,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import {
  AbstractControl,
  ControlContainer,
  FormArray,
  FormControl,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  isFormArray,
  isFormGroup,
  isLeafControl,
} from '@helaba/iso20022-lib/util';
import { ISO_LIB_LABELS, Labels } from '@helaba/iso20022-lib/config';

@Component({
  selector: 'app-form-value',
  imports: [ReactiveFormsModule, NgTemplateOutlet],
  templateUrl: './form-value.component.html',
  styleUrl: './form-value.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormValueComponent {
  fieldName = input.required<string>();
  fieldTemplate = input.required<TemplateRef<unknown>>();
  addLabelsToLeafControls = input<boolean>(false);
  level = input<number>(0); // For indentation/styling purposes

  controlContainer = inject(ControlContainer);

  // Inject effective labels signal from the library config
  labels: Signal<Labels> = inject(ISO_LIB_LABELS);

  // Expose 'Object' for template usage
  Object = Object;

  get parentFormGroup(): FormGroup<any> {
    return this.controlContainer.control as FormGroup<any>;
  }

  labelIdentifier = computed(() => {
    return this.getLabelIdentifier(this.fieldName());
  });

  labelForCurrentField = computed(() => this.labels()[this.labelIdentifier()]);

  fieldControl = computed<AbstractControl<any, any>>(() => {
    const control = this.parentFormGroup.get(this.fieldName());
    if (!control) {
      throw new Error(`Control not found for field name: ${this.fieldName()}`);
    }
    const controlIsLeafControl = isLeafControl(control);
    const controlIsFormGroup = isFormGroup(control);
    const controlIsFormArray = isFormArray(control);
    if (
      [controlIsLeafControl, controlIsFormGroup, controlIsFormArray].filter(
        Boolean
      ).length !== 1
    ) {
      throw new Error(
        `Expected a FormControl, FormGroup, or FormArray for field name ${this.fieldName()}`
      );
    }

    return control;
  });

  // Narrow to a leaf FormControl if applicable
  leafControl = computed<FormControl<string | null | undefined> | null>(() => {
    return this.getLeafControl(this.fieldControl());
  });

  getLeafControl(
    control: AbstractControl
  ): FormControl<string | null | undefined> | null {
    if (isLeafControl(control)) {
      return control as FormControl<string | null | undefined>;
    }
    return null;
  }

  // Narrow to a FormGroup if applicable
  formGroup = computed<FormGroup | null>(() => {
    return this.getFormGroup(this.fieldControl());
  });

  getFormGroup(control: AbstractControl): FormGroup | null {
    if (isFormGroup(control)) {
      return control as FormGroup<any>;
    }
    return null;
  }

  // Narrow to a FormArray if applicable
  formArray = computed<FormArray | null>(() => {
    const control = this.fieldControl();
    if (isFormArray(control)) {
      return control as FormArray;
    }
    return null;
  });

  isPrimitiveFormArray = computed<boolean>(() => {
    const formArray = this.formArray();
    if (!formArray) {
      return false;
    }
    // Check if all controls in the array are leaf controls
    return formArray.controls.every((control) => isLeafControl(control));
  });

  private getLabelIdentifier(fieldName: string): string {
    if (!fieldName.includes('.')) {
      return fieldName;
    }

    // Remove array index if present
    const parts = fieldName.split('.').filter((part) => !/^\d+$/.test(part));

    return parts.join('-');
  }
}
