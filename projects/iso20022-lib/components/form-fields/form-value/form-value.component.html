@if (leafControl(); as leafControl) { @if (addLabelsToLeafControls()) {
<label [for]="fieldName()" class="formLabel">
  {{ labelForCurrentField() }}
</label>
}
<ng-container
  *ngTemplateOutlet="
    fieldTemplate();
    context: { $implicit: fieldName(), formControl: leafControl }
  "
/>
} @if (formGroup(); as formGroup) {
<div class="formGroupContainer">
  @for (key of Object.keys(formGroup.controls); track key) {
  <div class="formGroupItem">
    <app-form-value
      [fieldName]="`${fieldName()}.${key}`"
      [addLabelsToLeafControls]="addLabelsToLeafControls()"
      [fieldTemplate]="fieldTemplate()"
      [level]="level() + 1"
      [attr.data-level]="level() + 1"
    />
  </div>
  }
</div>
} @if (formArray(); as formArray) {
<div class="formArrayContainer">
  @if (formArray.controls.length === 0) {
  <div class="emptyArray">TODO: Inject from client (No items)</div>
  } @else if (formArray.controls.length === 1) {
  <div class="arrayCount">TODO: Inject from client (1 item)</div>
  } @else {
  <div class="arrayCount">
    {{ formArray.controls.length }} TODO: Inject from client (items)
  </div>
  }
  <div class="formArrayItems">
    @for (control of formArray.controls; let index = $index; track index) {
    <div class="formArrayItem" [attr.data-index]="index">
      <div class="formArrayItemHeader">
        <span class="formArrayItemIndex"
          >TODO: Inject from client (Item) {{ index + 1 }}</span
        >
      </div>
      <app-form-value
        [fieldName]="`${fieldName()}.${index}`"
        [addLabelsToLeafControls]="!isPrimitiveFormArray()"
        [fieldTemplate]="fieldTemplate()"
        [level]="level() + 1"
        [attr.data-level]="level() + 1"
      />
    </div>
    }
  </div>
</div>
}
