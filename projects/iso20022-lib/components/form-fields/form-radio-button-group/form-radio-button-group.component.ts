import {
  ChangeDetectionStrategy,
  Component,
  input,
  TemplateRef,
  Type,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent, BaseFieldStructureComponent } from '../base-field';
import { SelectOption } from '@helaba/iso20022-lib/util';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-radio-button-group',
  imports: [
    ReactiveFormsModule,
    BaseFieldStructureComponent,
    NgTemplateOutlet,
    FormValueComponent,
  ],
  templateUrl: './form-radio-button-group.component.html',
  styleUrl: './form-radio-button-group.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormRadioButtonGroupComponent<
  T extends string
> extends BaseFieldComponent {
  override label = input.required<string>();
  override fieldName = input.required<string>();
  override isReadOnly = input.required<boolean>();
  override fieldPrefix = input.required<string>();
  override errorTemplate = input.required<TemplateRef<unknown>>();
  override formValueFieldTemplate = input.required<TemplateRef<unknown>>();
  override fieldsetComponent = input.required<Type<unknown>>();

  fieldTemplate = input.required<TemplateRef<unknown>>();
  options = input.required<SelectOption<T>[]>();
}
