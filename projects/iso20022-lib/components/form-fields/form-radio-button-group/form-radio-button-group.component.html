<app-base-field-structure
  [label]="label()"
  [fieldName]="fieldName()"
  [fieldId]="fieldId()"
  [errorTemplate]="formRadioButtonGroupErrorTemplate"
  [useLabel]="false"
  [fieldsetComponent]="fieldsetComponent()"
>
  @if (isReadOnly()) {
  <app-form-value
    [fieldName]="fieldName()"
    [fieldTemplate]="formValueFieldTemplate()"
  />
  } @else {
  <div class="radioButtonGroup">
    @for (option of options(); track $index) {
    <div class="radioButtonGroupElement">
      <ng-container
        *ngTemplateOutlet="fieldTemplate(); context: {$implicit: `${fieldId()}-${option.value}`, optionValue: option.value}"
      />
      <label [for]="`${fieldId()}-${option.value}`">{{ option.label }}</label>
    </div>
    }
  </div>
  }
</app-base-field-structure>

<ng-template #formRadioButtonGroupErrorTemplate let-fieldId="fieldId">
  <ng-container
    *ngTemplateOutlet="
      errorTemplate();
      context: {
        $implicit: fieldId,
      }
    "
  />
</ng-template>
