import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  signal,
  TemplateRef,
  Type,
} from '@angular/core';

import {
  ControlContainer,
  FormArray,
  FormGroup,
  FormGroupDirective,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { NgTemplateOutlet } from '@angular/common';
import { applyRelevantRules } from '@helaba/iso20022-lib/util';
import { Rule } from '@helaba/iso20022-lib/rules';
import { BaseFieldComponent, BaseFieldStructureComponent } from '../base-field';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-group-array',
  imports: [
    ReactiveFormsModule,
    NgTemplateOutlet,
    BaseFieldStructureComponent,
    FormValueComponent,
  ],
  templateUrl: './form-group-array.component.html',
  styleUrl: './form-group-array.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormGroupArrayComponent extends BaseFieldComponent {
  override label = input.required<string>();
  override fieldName = input.required<string>();
  override isReadOnly = input.required<boolean>();
  override fieldPrefix = input.required<string>();
  override errorTemplate = input.required<TemplateRef<unknown>>();
  override formValueFieldTemplate = input.required<TemplateRef<unknown>>();
  override fieldsetComponent = input.required<Type<unknown>>();

  addItemButtonTemplate = input.required<TemplateRef<unknown>>();
  removeItemButtonTemplate = input.required<TemplateRef<unknown>>();

  fb = inject(NonNullableFormBuilder);

  // Factory function to create new form groups
  groupFactory = input.required<() => FormGroup>();
  // Template for rendering each group item
  itemTemplate = input.required<TemplateRef<unknown>>();
  // Validation rules of the current form group, we need to apply them to the newly created field when an array entry is added.
  validationRules = input.required<Rule<string, undefined>[]>();

  itemCount = signal(0); // Track the number of items in the array

  formArray = computed(() => {
    const field = this.formGroup.get(this.fieldName());

    if (!(field && field instanceof FormArray)) {
      throw new Error(
        `Expected to find a FormArray for field name ${this.fieldName()}`
      );
    }
    return field;
  });

  controls = computed(() => {
    const array = this.formArray();
    const arrayControls = array.controls;
    if (!arrayControls || !Array.isArray(arrayControls)) {
      throw new Error(
        `Expected the formArray to contain a list of controls for field name ${this.fieldName()}`
      );
    }
    return arrayControls;
  });

  // Arrow function to preserve 'this' context
  addItem = () => {
    const fieldArray = this.formArray();
    if (fieldArray) {
      const newGroup = this.groupFactory()();
      for (const [name, control] of Object.entries(newGroup.controls)) {
        applyRelevantRules(
          `${this.fieldName()}.${this.itemCount()}.${name}`,
          control,
          this.validationRules()
        );
      }
      fieldArray.push(newGroup);
      this.itemCount.update((count) => count + 1);
    }
  };

  removeItem = (index: number) => {
    const fieldArray = this.formArray();
    if (fieldArray) {
      fieldArray.removeAt(index);
      this.itemCount.update((count) => Math.max(count - 1, 0));
    }
  };
}
