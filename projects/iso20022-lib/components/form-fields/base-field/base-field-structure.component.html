<div class="formField">
  @if (useLabel()) {
  <div class="formFieldWithLabel">
    <label [for]="fieldId()" class="formFieldLabel">{{ label() }}</label>
    <ng-container *ngTemplateOutlet="contentTemplate" />
  </div>
  } @else {
  <ng-container
    *ngComponentOutlet="
      fieldsetComponent();
      inputs: { legend: label() };
      content: content
    "
  />
  }
  <ng-container
    *ngTemplateOutlet="
      errorTemplate();
      context: { $implicit: fieldName(), fieldId: fieldId() }
    "
  />
</div>

<ng-template #contentTemplate>
  <ng-content />
</ng-template>
