import {
  ChangeDetectionStrategy,
  Component,
  computed,
  contentChild,
  effect,
  inject,
  input,
  Signal,
  TemplateRef,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { AbstractControl, ControlContainer, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { FormErrorsService } from '@helaba/iso20022-lib/services';
import { getFieldIdsFromFormGroup } from '@helaba/iso20022-lib/util';

@Component({
  selector: 'app-field-errors',
  imports: [NgTemplateOutlet],
  templateUrl: './field-errors.component.html',
  styleUrl: './field-errors.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldErrorsComponent {
  fieldName = input.required<string>();
  fieldId = input.required<string>();

  controlContainer = inject(ControlContainer);
  #formErrorsService = inject(FormErrorsService);

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  control = computed<AbstractControl>(() => {
    if (!this.formGroup) {
      console.warn(
        'FieldErrorsComponent: formGroup is not defined. Ensure that this component is used within a form context.'
      );
    }

    const formControl = this.formGroup.get(this.fieldName());
    if (!formControl) {
      throw new Error(
        `FieldErrorsComponent: No control found for field name "${this.fieldName()}" in form group with field ids ${getFieldIdsFromFormGroup(
          this.formGroup
        )}. Ensure that the fieldName matches a control in the local form group.`
      );
    }

    return formControl;
  });

  errorTemplate = contentChild<TemplateRef<{ $implicit: string }>>(TemplateRef);

  // To keep track of the subscription to the control's statusChanges.
  #activeSubscriptions: Subscription[] = [];

  constructor() {
    effect((onCleanup) => {
      // Clean up any existing subscription when the control changes or component is destroyed.
      this.cleanup();

      const currentControl = this.control();

      this.#activeSubscriptions.push(
        currentControl.statusChanges.subscribe(() => {
          const errors = currentControl.errors;

          // Update the errors in the central FormErrorsService.
          const erroneousRules = errors ? Object.keys(errors) : null;
          this.#formErrorsService.updateFieldErrors(
            this.fieldId(),
            erroneousRules
          );
        })
      );

      onCleanup(() => {
        this.cleanup();
      });
    });
  }

  errorMessages: Signal<string[]> = computed(() => {
    const fieldErrors =
      this.#formErrorsService.fieldErrors().get(this.fieldId()) || [];
    return fieldErrors.map(
      (ruleId: string) =>
        this.#formErrorsService.errorMessages().get(ruleId) || ruleId
    );
  });

  private cleanup() {
    for (const sub of this.#activeSubscriptions) {
      sub.unsubscribe();
    }
    this.#activeSubscriptions = [];
  }
}
